<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

#[AsCommand(
    name: 'debug:env-vars',
    description: 'Debug environment variables for MongoDB'
)]
class DebugEnvCommand extends Command
{
    public function __construct(
        private ParameterBagInterface $parameterBag
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('=== Environment Variables Debug ===');
        
        // Check environment variables
        $envVars = [
            'MONGO_DB_URL',
            'MONGODB_DB',
            'MONGO_ATLAS_BASE_URL',
            'MONGO_APP',
            'MONGO_DATABASE',
            'MONGO_DATASOURCE'
        ];
        
        foreach ($envVars as $var) {
            $value = $_ENV[$var] ?? $_SERVER[$var] ?? getenv($var) ?: 'NOT SET';
            $output->writeln(sprintf('%s: %s', $var, $value === 'NOT SET' ? '<error>NOT SET</error>' : '<info>' . $value . '</info>'));
        }
        
        $output->writeln('');
        $output->writeln('=== Symfony Parameters ===');
        
        // Check Symfony parameters
        $parameters = [
            'mongodb.url',
            'mongodb.db',
            'mongo_db.url',
            'mongo_db.database'
        ];
        
        foreach ($parameters as $param) {
            try {
                $value = $this->parameterBag->get($param);
                $output->writeln(sprintf('%s: <info>%s</info>', $param, $value));
            } catch (\Exception $e) {
                $output->writeln(sprintf('%s: <error>%s</error>', $param, $e->getMessage()));
            }
        }
        
        return Command::SUCCESS;
    }
}

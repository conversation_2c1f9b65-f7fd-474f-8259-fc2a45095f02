<?php

namespace App\Manager;

use App\Helper\BrandHelper;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\MarketHelper;
use App\Helper\SuccessResponse;
use App\Service\FavoriteDealerService;
// use App\Service\EnhancedFavoriteDealerService; // Temporarily disabled
use App\Trait\LoggerTrait;

use App\Transformer\XpResponseTransformer;
use App\Transformer\XfEmeaResponseTransformer;
use App\Transformer\xfEmeaParameterRequestTransformer;
use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Document\Settings;

class FavoriteDealerManager
{
    use LoggerTrait;

    const COLLECTION = 'userData';

    private const COLLECTION_SETTINGS = 'settings';
    private Serializer $serializer;


    public function __construct(
        private ValidatorInterface $validator,
        private MongoDBService $mongoDBService,
        private FavoriteDealerService $favoriteDealerService,
        // private EnhancedFavoriteDealerService $enhancedFavoriteDealerService, // Temporarily disabled
        private XpResponseTransformer $xpResponseTransformer,

    ) {
        $this->serializer = new Serializer([new ObjectNormalizer()]);
    }

    public function saveFavoriteDealer(array $params): ResponseArrayFormat
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->saveFavoriteDealerXP($params);
        }
        return $this->saveFavoriteDealerXF($params);
    }

    private function saveFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start saving XP favorite dealer in database', [
            'params' => $params,
        ]);

        try {
            $userId = $params['userId'];
            $brand = strtolower($params['brand']);
            $brandParameters = [
                'country' => $params['country'],
                'idSiteGeo' => $params['siteGeo'],
                'language' => $params['language']
            ];

            // Find or create user data
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                // Create new user data if not exists
                $userData = new UserData();
                $userData->setUserId($userId);
            }

            // Set preferred dealer for the brand
            $userData->setPreferredDealerForBrand($brand, $brandParameters);

            // Save the document
            $this->mongoDBService->save($userData);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully added', [
                'userId' => $userId,
                'brand' => $brand,
                'dealerData' => $brandParameters,
            ]);

            return new SuccessResponse("Favourite dealer successfully added!", Response::HTTP_OK);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error saving XP favorite dealer', [
                'params' => $params,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse("Cannot save favorite dealer in database", Response::HTTP_BAD_REQUEST);
        }
    }

    private function saveFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start checking if vin exists for XF favorite dealer in database', [
            'params' => $params,
        ]);

        try {
            $siteGeo = $params['siteGeo'];
            $userId = $params['userId'];
            $vin = $params['vin'];

            // Find user data
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User not found for XF favorite dealer', [
                    'userId' => $userId,
                ]);
                return new ErrorResponse("The VIN is not part of the user's garage", Response::HTTP_FORBIDDEN);
            }

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Failed to find the vin for XF favorite dealer in database', [
                    'userId' => $userId,
                    'vin' => $vin,
                ]);
                return new ErrorResponse("The VIN is not part of the user's garage", Response::HTTP_FORBIDDEN);
            }

            // Set preferred dealer for the vehicle
            $brandParameters = [
                'country' => $params['country'],
                'idSiteGeo' => $siteGeo
            ];
            $vehicle->setPreferredDealer($brandParameters);

            // Save the document
            $this->mongoDBService->save($userData);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XF Favorite dealer is successfully added', [
                'userId' => $userId,
                'vin' => $vin,
                'dealerData' => $brandParameters,
            ]);

            return new SuccessResponse("Favourite dealer successfully added!", Response::HTTP_OK);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error saving XF favorite dealer', [
                'params' => $params,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse("Cannot save favorite dealer in database", Response::HTTP_BAD_REQUEST);
        }
    }

    public function deleteFavoriteDealer(array $params)
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->deleteFavoriteDealerXP($params);
        } elseif (BrandHelper::isXF($brand)) {
            return $this->deleteFavoriteDealerXF($params);
        }
    }

    private function deleteFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Deleting XP favorite dealer from database', [
            'params' => $params,
        ]);

        try {
            $userId = $params['userId'];
            $brand = strtolower($params['brand']);

            // Find user data
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User not found for XP favorite dealer deletion', [
                    'userId' => $userId,
                ]);
                return new ErrorResponse("Cannot delete favorite dealer from database", Response::HTTP_BAD_REQUEST);
            }

            // Remove preferred dealer for the brand
            $userData->removePreferredDealerForBrand($brand);

            // Save the document
            $this->mongoDBService->save($userData);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully deleted', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            return new SuccessResponse("successfully deleted!", Response::HTTP_OK);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error deleting XP favorite dealer', [
                'params' => $params,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse("Cannot delete favorite dealer from database", Response::HTTP_BAD_REQUEST);
        }
    }

    private function deleteFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Start checking if vin exists for XF favorite dealer in database', [
            'params' => $params,
        ]);

        try {
            $userId = $params['userId'];
            $vin = $params['vin'];

            // Find user data
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User not found for XF favorite dealer deletion', [
                    'userId' => $userId,
                ]);
                return new ErrorResponse("The VIN is not part of the user's garage", Response::HTTP_FORBIDDEN);
            }

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Failed to find the vin for XF favorite dealer in database', [
                    'userId' => $userId,
                    'vin' => $vin,
                ]);
                return new ErrorResponse("The VIN is not part of the user's garage", Response::HTTP_FORBIDDEN);
            }

            // Remove preferred dealer from the vehicle
            $vehicle->setPreferredDealer(null);

            // Save the document
            $this->mongoDBService->save($userData);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XF Favorite dealer is successfully deleted', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            return new SuccessResponse("successfully deleted!", Response::HTTP_OK);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error deleting XF favorite dealer', [
                'params' => $params,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse("Cannot delete favorite dealer from database", Response::HTTP_BAD_REQUEST);
        }
    }

    public function getFavoriteDealer(array $params): ResponseArrayFormat
    {
        $brand = $params['brand'];
        if (BrandHelper::isXP($brand)) {
            return $this->getFavoriteDealerXP($params);
        }
        return $this->getFavoriteDealerXF($params);
    }

    public function getFavoriteDealerXP(array $params): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting XP favorite dealer from database', [
                'params' => $params,
            ]);

            $userId = $params['userId'];
            $brand = strtolower($params['brand']);

            // Find user data using MongoDB ODM
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User not found for XP favorite dealer', [
                    'userId' => $userId,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }

            // Get preferred dealer for the brand
            $site_geo = $userData->getPreferredDealerForBrand($brand);
            if ($site_geo === null) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No preferred dealer found for brand', [
                    'userId' => $userId,
                    'brand' => $brand,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XP Favorite dealer is successfully fetched', [
                'userId' => $userId,
                'brand' => $brand,
                'dealerData' => $site_geo,
            ]);

            $params['country'] = $site_geo['country'] ?? '';
            $params['language'] = $site_geo['language'] ?? '';
            $params['siteGeo'] = $site_geo['idSiteGeo'] ?? '';

            // Try enhanced service first, fallback to original service (temporarily disabled)
            // $response = $this->enhancedFavoriteDealerService->getXpDealerDetails($params);
            // if ($response->getCode() != 200) {
            //     $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Enhanced service failed, falling back to original service');
                $response = $this->favoriteDealerService->getXpDealerDetails($params);
            // }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Favorite dealer is successfully fetched', [
                'response' => $response->getData(),
            ]);
            if (Response::HTTP_OK == $response->getCode()) {
                $data = $response->getData()['success'] ?? [];
                if (empty($data)) {
                    return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
                }
                $params['o2x'] = $this->getO2xSettings($params['brand'], $params['source']);
                $response = $this->xpResponseTransformer->mapper($data, $params);
                $dealerResponse = ["Dealer" => $response];
                return new SuccessResponse($dealerResponse, Response::HTTP_OK);
            }
            $responseData = $response->getData();
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
            return new ErrorResponse($result, code: $response->getCode());

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting favorite dealer: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e,
            ]);
            return new ErrorResponse("Error getting favorite dealer", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getFavoriteDealerXF(array $params): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Starting XF favorite dealer retrieval', [
                'params' => $params,
            ]);

            $userId = $params['userId'];
            $vehicleId = $params['vin'];

            // Find user data using MongoDB ODM
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
            if (!$userData) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User not found for XF favorite dealer', [
                    'userId' => $userId,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User found via MongoDB ODM', [
                'userId' => $userId,
                'vehicleCount' => count($userData->getVehicles()),
            ]);

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vehicleId);
            if (!$vehicle) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found via MongoDB ODM', [
                    'vin' => $vehicleId,
                    'userId' => $userId,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle found via MongoDB ODM', [
                'vin' => $vehicleId,
                'brand' => $vehicle->getBrand(),
                'hasPreferredDealer' => $vehicle->getPreferredDealer() !== null,
            ]);

            // Get preferred dealer from the vehicle
            $preferredDealer = $vehicle->getPreferredDealer();
            if (!$preferredDealer || empty($preferredDealer['idSiteGeo'])) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No preferred dealer found via MongoDB ODM', [
                    'vin' => $vehicleId,
                    'preferredDealer' => $preferredDealer,
                ]);
                return new ErrorResponse("No favorite dealer defined for the user", Response::HTTP_FORBIDDEN);
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Preferred dealer found via MongoDB ODM', [
                'preferredDealer' => $preferredDealer,
            ]);

            $params['country'] = $preferredDealer['country'] ?? "";
            $params['siteGeo'] = $preferredDealer['idSiteGeo'] ?? "";

            // Try enhanced service first, fallback to original service (temporarily disabled)
            // $response = $this->enhancedFavoriteDealerService->getXfDealerDetails($params);
            // if ($response->getCode() != 200) {
            //     $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Enhanced service failed, falling back to original service');
                $response = $this->favoriteDealerService->getXfDealerDetails($params);
            // }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' XF favorite dealer fetched successfully via MongoDB ODM', [
                'params' => $params,
                'response_code' => $response->getCode(),
            ]);

            if ($response->getCode() !== 200) {
                $responseData = $response->getData();
                $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : $responseData;
                return new ErrorResponse($result, code: $response->getCode());
            }

            $data = $response->getData()['success'] ?? [];
            if (empty($data)) {
                return new ErrorResponse('No favorite dealer defined for the user', Response::HTTP_FORBIDDEN);
            }

            $marketCode = MarketHelper::getMarket($params['country']);
            $brandCode = BrandHelper::getBrandCode($params['brand']);
            $params = xfEmeaParameterRequestTransformer::mapper(
                $params,
                $marketCode,
                $brandCode
            );
            $xfEmeaResponse = XfEmeaResponseTransformer::mapper(
                $data ?? [],
                $params
            );

            return new SuccessResponse($xfEmeaResponse->getSuccess(), Response::HTTP_OK);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting favorite dealer: ' . $e->getMessage(), [
                'params' => $params,
                'exception' => $e,
            ]);
            return new ErrorResponse("Error getting favorite dealer", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function getO2xSettings(string $brand, $source = "APP"): array
    {
        try {
            $filter = [
                'brand' => $brand,
                'source' => $source,
                'culture' => '',
                '$or' => [
                    ["settingsData.o2x.code" => "o2x"],
                    ["settingsData.config.code" => "o2x"]
                ]
            ];

            // Use MongoDB ODM to find settings
            $settings = $this->mongoDBService->findSettingsByFilter($filter);
            if (!$settings) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No settings found for o2x', [
                    'brand' => $brand,
                    'source' => $source,
                ]);
                return [];
            }

            $settingsDatas = $settings->getSettingsData();
            if ('APP' == $source) {
                $data = current(array_filter($settingsDatas, function ($item) {
                    $code = $item['config']['code'] ?? '';
                    return $code == 'o2x';
                }));
                if ($data) {
                    $config = $data['config'] ?? [];
                    $result = [...$data, ...$config];
                    unset($result['config']);
                } else {
                    $result = $settingsDatas['o2x'] ?? [];
                }

                return $result ?? [];
            }
            return $settingsDatas['o2x'] ?? [];
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf('%s: Error while getting o2x settings ', __METHOD__),
                [
                    'exception_code' => $e->getCode(),
                    'exception_message' => $e->getMessage(),
                ]
            );
            return [];
        }
    }


}

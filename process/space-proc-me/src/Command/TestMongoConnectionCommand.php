<?php

namespace App\Command;

use Space\MongoDocuments\Service\MongoDBService;
use Space\MongoDocuments\Document\UserData;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-mongo-connection',
    description: 'Test MongoDB connection and basic operations',
)]
class TestMongoConnectionCommand extends Command
{
    public function __construct(
        private MongoDBService $mongoDBService
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Testing MongoDB Connection');

        try {
            // Test 1: Try to find a user (should not throw exception even if not found)
            $io->section('Test 1: Finding a user');
            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => 'test123']);
            if ($userData) {
                $io->success('Found user: ' . $userData->getUserId());
            } else {
                $io->info('User not found (this is expected for test user)');
            }

            // Test 2: Try to create and save a test user
            $io->section('Test 2: Creating a test user');
            $testUser = new UserData();
            $testUser->setUserId('test-connection-' . time());
            
            $this->mongoDBService->save($testUser);
            $io->success('Successfully created and saved test user: ' . $testUser->getUserId());

            // Test 3: Try to find the created user
            $io->section('Test 3: Finding the created user');
            $foundUser = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $testUser->getUserId()]);
            if ($foundUser) {
                $io->success('Successfully found the created user: ' . $foundUser->getUserId());
            } else {
                $io->error('Could not find the created user');
                return Command::FAILURE;
            }

            // Test 4: Test preferred dealer operations
            $io->section('Test 4: Testing preferred dealer operations');
            $foundUser->setPreferredDealerForBrand('ds', [
                'country' => 'FR',
                'idSiteGeo' => 'TEST123',
                'language' => 'fr'
            ]);
            
            $this->mongoDBService->save($foundUser);
            $io->success('Successfully set preferred dealer for brand');

            // Test 5: Retrieve preferred dealer
            $io->section('Test 5: Retrieving preferred dealer');
            $preferredDealer = $foundUser->getPreferredDealerForBrand('ds');
            if ($preferredDealer) {
                $io->success('Successfully retrieved preferred dealer: ' . json_encode($preferredDealer));
            } else {
                $io->error('Could not retrieve preferred dealer');
                return Command::FAILURE;
            }

            // Clean up: Remove test user
            $io->section('Cleanup: Removing test user');
            $this->mongoDBService->remove($foundUser);
            $io->success('Successfully removed test user');

            $io->success('All MongoDB connection tests passed!');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('MongoDB connection test failed: ' . $e->getMessage());
            $io->error('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }
}

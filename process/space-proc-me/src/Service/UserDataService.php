<?php

namespace App\Service;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\HttpFoundation\Response;

/**
 * UserDataService handles user data operations using MongoDB ODM
 * Following the established pattern from space-proc-shop microservice
 */
class UserDataService
{
    use LoggerTrait;

    public function __construct(
        private MongoDBService $mongoDBService
    ) {
    }

    /**
     * Find user by ID
     */
    public function findUserById(string $userId): ?UserData
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Finding user by ID', [
                'userId' => $userId,
            ]);

            return $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error finding user by ID', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Save user data
     */
    public function saveUserData(UserData $userData): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Saving user data', [
                'userId' => $userData->getUserId(),
            ]);

            $this->mongoDBService->save($userData);

            return new SuccessResponse('User data saved successfully', Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error saving user data', [
                'userId' => $userData->getUserId(),
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot save user data', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Set preferred dealer for brand (XP)
     */
    public function setPreferredDealerForBrand(string $userId, string $brand, array $dealerData): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Setting preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
                'dealerData' => $dealerData,
            ]);

            // Find or create user data
            $userData = $this->findUserById($userId);
            if (!$userData) {
                $userData = new UserData();
                $userData->setUserId($userId);
            }

            // Set preferred dealer for the brand
            $userData->setPreferredDealerForBrand($brand, $dealerData);

            // Save the document
            return $this->saveUserData($userData);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error setting preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot save preferred dealer', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Remove preferred dealer for brand (XP)
     */
    public function removePreferredDealerForBrand(string $userId, string $brand): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Removing preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            // Remove preferred dealer for the brand
            $userData->removePreferredDealerForBrand($brand);

            // Save the document
            return $this->saveUserData($userData);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error removing preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot remove preferred dealer', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get preferred dealer for brand (XP)
     */
    public function getPreferredDealerForBrand(string $userId, string $brand): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return null;
            }

            return $userData->getPreferredDealerForBrand($brand);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Set preferred dealer for vehicle (XF)
     */
    public function setPreferredDealerForVehicle(string $userId, string $vin, array $dealerData): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Setting preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'dealerData' => $dealerData,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return new ErrorResponse('Vehicle not found', Response::HTTP_NOT_FOUND);
            }

            // Set preferred dealer for the vehicle
            $vehicle->setPreferredDealer($dealerData);

            // Save the document
            return $this->saveUserData($userData);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error setting preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot save preferred dealer', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Remove preferred dealer for vehicle (XF)
     */
    public function removePreferredDealerForVehicle(string $userId, string $vin): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Removing preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return new ErrorResponse('Vehicle not found', Response::HTTP_NOT_FOUND);
            }

            // Remove preferred dealer from the vehicle
            $vehicle->setPreferredDealer(null);

            // Save the document
            return $this->saveUserData($userData);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error removing preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot remove preferred dealer', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get preferred dealer for vehicle (XF)
     */
    public function getPreferredDealerForVehicle(string $userId, string $vin): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return null;
            }

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return null;
            }

            return $vehicle->getPreferredDealer();
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }
}

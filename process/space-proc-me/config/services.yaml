# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    # MongoDB Atlas API (legacy)
    mongo_db.app: "%env(MONGO_APP)%"
    mongo_db.database: "%env(MONGO_DATABASE)%"
    mongo_db.datasource: "%env(MONGO_DATASOURCE)%"
    mongo_db.url: "%env(MONGO_ATLAS_BASE_URL)%"

    # MongoDB ODM
    mongodb.url: "%env(MONGO_DB_URL)%"
    mongodb.db: "%env(MONGODB_DB)%"
    env(MONGODB_URL): ''
    env(MONGODB_DB): ''

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\Connector\MongoAtlasApiConnector:
        arguments:
            $mongoApp: '%mongo_db.app%'

    App\Service\MongoAtlasQueryService:
        arguments:
            $database: '%mongo_db.database%'
            $dataSource: '%mongo_db.datasource%'

    App\Connector\SystemIdpConnector:
        arguments:
            $url: "%env(MS_SYS_IDP_URL)%"

    App\Connector\SysAPDVConnector:
        arguments:
            $url: "%env(MS_SYS_APDV_URL)%"

    App\Connector\SysServiceAdvisorConnector:
        arguments:
            $url: "%env(MS_SYS_SA_URL)%"

    App\Connector\SystemUserDBConnector:
        arguments:
            $url: "%env(MS_SYS_USER_DB_URL)%"

    # MongoDB ODM services
    App\Service\MongoDBService:
        arguments:
            $documentManager: '@doctrine_mongodb.odm.document_manager'
            $logger: '@logger'

    # Space MongoDB Documents Service
    Space\MongoDocuments\Service\MongoDBService:
        arguments:
            $documentManager: '@doctrine_mongodb.odm.document_manager'
            $logger: '@logger'

    # UserDataService with MongoDB ODM
    App\Service\UserDataService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'

    # SettingsService with MongoDB ODM
    App\Service\SettingsService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'